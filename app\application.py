from flask import Flask,render_template,request,session,redirect,url_for
from app.components.retriever import create_qa_chain
from dotenv import load_dotenv
import os
from app.common.logger import get_logger

load_dotenv()

GROQ_API_KEY = os.environ.get("GROQ_API_KEY")

app = Flask(__name__)
app.secret_key = os.urandom(24)
load_dotenv()

logger = get_logger(__name__)

from markupsafe import Markup
def nl2br(value):
    # Ensure value is a string before processing
    if not isinstance(value, str):
        value = str(value)
    return Markup(value.replace("\n" , "<br>\n"))

app.jinja_env.filters['nl2br'] = nl2br

@app.route("/" , methods=["GET","POST"])
def index():
    if "messages" not in session:
        session["messages"]=[]

    if request.method=="POST":
        user_input = request.form.get("prompt")

        if user_input:
            messages = session["messages"]
            messages.append({"role" : "user" , "content":user_input})
            session["messages"] = messages

            try:
                qa_chain = create_qa_chain()
                if qa_chain is None:
                    raise Exception("QA chain could not be created (LLM or VectorStore issue)")

                # RetrievalQA expects "query" parameter, not "question"
                response = qa_chain.invoke({"query": user_input})

                # Debug logging to understand response format
                logger.info(f"DEBUG: Response type: {type(response)}")
                logger.info(f"DEBUG: Response content: {response}")

                # Extract the result from the response dictionary
                if isinstance(response, dict):
                    content = response.get("result", "No result found")
                else:
                    content = str(response)

                # Ensure content is a string
                if not isinstance(content, str):
                    content = str(content)

                logger.info(f"DEBUG: Final content type: {type(content)}")
                logger.info(f"DEBUG: Final content: {content}")

                messages.append({"role": "assistant", "content": content})
                session["messages"] = messages

            except Exception as e:
                error_msg = f"Error : {str(e)}"
                logger.error(f"Application error: {error_msg}")
                return render_template("index.html" , messages = session["messages"] , error = error_msg)
            
        return redirect(url_for("index"))
    return render_template("index.html" , messages=session.get("messages" , []))

@app.route("/clear")
def clear():
    session.pop("messages" , None)
    return redirect(url_for("index"))

if __name__=="__main__":
    app.run(host="0.0.0.0" , port=5000 , debug=False , use_reloader = False)