2025-07-17 17:19:20,753 - INFO - Making the vector store...
2025-07-17 17:19:20,754 - INFO - Loading files from data/
2025-07-17 17:19:30,465 - INFO - Successfully loaded 759 documents
2025-07-17 17:19:30,465 - INFO - Splitting 759 documents into chunks...
2025-07-17 17:19:30,641 - INFO - Successfully split documents into 7080 chunks.
2025-07-17 17:19:30,641 - INFO - Creating a vector store...
2025-07-17 17:19:30,641 - INFO - Loading OpenAI embedding model...
2025-07-17 17:19:30,641 - ERROR - Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 10
2025-07-17 17:19:30,642 - ERROR - Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 10 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35
2025-07-17 17:19:30,643 - ERROR - Error creating vector store: Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 10 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\data_loader.py | Line: 17
2025-07-17 17:21:49,969 - INFO - Making the vector store...
2025-07-17 17:21:49,970 - INFO - Loading files from data/
2025-07-17 17:21:59,738 - INFO - Successfully loaded 759 documents
2025-07-17 17:21:59,738 - INFO - Splitting 759 documents into chunks...
2025-07-17 17:21:59,902 - INFO - Successfully split documents into 7080 chunks.
2025-07-17 17:21:59,902 - INFO - Creating a vector store...
2025-07-17 17:21:59,902 - INFO - Loading OpenAI embedding model...
2025-07-17 17:21:59,902 - ERROR - Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11
2025-07-17 17:21:59,902 - ERROR - Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35
2025-07-17 17:21:59,902 - ERROR - Error creating vector store: Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\data_loader.py | Line: 17
2025-07-17 17:25:23,158 - INFO - Making the vector store...
2025-07-17 17:25:23,158 - INFO - Loading files from data/
2025-07-17 17:25:32,950 - INFO - Successfully loaded 759 documents
2025-07-17 17:25:32,950 - INFO - Splitting 759 documents into chunks...
2025-07-17 17:25:33,116 - INFO - Successfully split documents into 7080 chunks.
2025-07-17 17:25:33,116 - INFO - Creating a vector store...
2025-07-17 17:25:33,116 - INFO - Loading OpenAI embedding model...
2025-07-17 17:25:33,116 - ERROR - Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11
2025-07-17 17:25:33,117 - ERROR - Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35
2025-07-17 17:25:33,117 - ERROR - Error creating vector store: Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\data_loader.py | Line: 17
