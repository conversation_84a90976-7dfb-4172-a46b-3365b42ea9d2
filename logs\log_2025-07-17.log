2025-07-17 17:19:20,753 - INFO - Making the vector store...
2025-07-17 17:19:20,754 - INFO - Loading files from data/
2025-07-17 17:19:30,465 - INFO - Successfully loaded 759 documents
2025-07-17 17:19:30,465 - INFO - Splitting 759 documents into chunks...
2025-07-17 17:19:30,641 - INFO - Successfully split documents into 7080 chunks.
2025-07-17 17:19:30,641 - INFO - Creating a vector store...
2025-07-17 17:19:30,641 - INFO - Loading OpenAI embedding model...
2025-07-17 17:19:30,641 - ERROR - Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 10
2025-07-17 17:19:30,642 - ERROR - Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 10 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35
2025-07-17 17:19:30,643 - ERROR - Error creating vector store: Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 10 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\data_loader.py | Line: 17
2025-07-17 17:21:49,969 - INFO - Making the vector store...
2025-07-17 17:21:49,970 - INFO - Loading files from data/
2025-07-17 17:21:59,738 - INFO - Successfully loaded 759 documents
2025-07-17 17:21:59,738 - INFO - Splitting 759 documents into chunks...
2025-07-17 17:21:59,902 - INFO - Successfully split documents into 7080 chunks.
2025-07-17 17:21:59,902 - INFO - Creating a vector store...
2025-07-17 17:21:59,902 - INFO - Loading OpenAI embedding model...
2025-07-17 17:21:59,902 - ERROR - Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11
2025-07-17 17:21:59,902 - ERROR - Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35
2025-07-17 17:21:59,902 - ERROR - Error creating vector store: Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\data_loader.py | Line: 17
2025-07-17 17:25:23,158 - INFO - Making the vector store...
2025-07-17 17:25:23,158 - INFO - Loading files from data/
2025-07-17 17:25:32,950 - INFO - Successfully loaded 759 documents
2025-07-17 17:25:32,950 - INFO - Splitting 759 documents into chunks...
2025-07-17 17:25:33,116 - INFO - Successfully split documents into 7080 chunks.
2025-07-17 17:25:33,116 - INFO - Creating a vector store...
2025-07-17 17:25:33,116 - INFO - Loading OpenAI embedding model...
2025-07-17 17:25:33,116 - ERROR - Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11
2025-07-17 17:25:33,117 - ERROR - Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35
2025-07-17 17:25:33,117 - ERROR - Error creating vector store: Error creating vector store: Error loading OpenAI embedding model: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\emeddings.py | Line: 11 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\vector_store.py | Line: 35 | Error: None | File: C:\Users\<USER>\Downloads\Medical RAG Chatbot\app\components\data_loader.py | Line: 17
2025-07-17 17:43:46,648 - INFO - Making the vector store...
2025-07-17 17:43:46,648 - INFO - Loading files from data/
2025-07-17 17:43:57,005 - INFO - Successfully loaded 759 documents
2025-07-17 17:43:57,005 - INFO - Splitting 759 documents into chunks...
2025-07-17 17:43:57,170 - INFO - Successfully split documents into 7080 chunks.
2025-07-17 17:43:57,170 - INFO - Creating a vector store...
2025-07-17 17:43:57,170 - INFO - Loading OpenAI embedding model...
2025-07-17 17:43:57,881 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 17:44:01,806 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 17:44:08,153 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 17:44:15,388 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 17:44:20,619 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 17:44:31,644 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 17:44:42,799 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 17:44:54,343 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 17:44:58,032 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 17:44:58,359 - INFO - Loading faiss with AVX512 support.
2025-07-17 17:44:58,359 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-07-17 17:44:58,359 - INFO - Loading faiss with AVX2 support.
2025-07-17 17:44:58,933 - INFO - Successfully loaded faiss with AVX2 support.
2025-07-17 17:44:58,939 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-17 17:44:59,349 - INFO - Successfully created a vector store...
2025-07-17 17:44:59,415 - INFO - Successfully saved vector store
2025-07-17 17:44:59,420 - INFO - Successfully created the vector store...
2025-07-17 21:57:22,364 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-17 21:57:22,364 - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 21:58:02,161 - INFO - 127.0.0.1 - - [17/Jul/2025 21:58:02] "GET / HTTP/1.1" 200 -
2025-07-17 21:58:02,438 - INFO - 127.0.0.1 - - [17/Jul/2025 21:58:02] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-17 21:58:33,188 - INFO - Loading vector store...
2025-07-17 21:58:33,188 - INFO - Loading vector store...
2025-07-17 21:58:33,288 - INFO - Loading faiss with AVX512 support.
2025-07-17 21:58:33,289 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-07-17 21:58:33,290 - INFO - Loading faiss with AVX2 support.
2025-07-17 21:58:33,349 - INFO - Successfully loaded faiss with AVX2 support.
2025-07-17 21:58:33,355 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-17 21:58:33,409 - WARNING - `embedding_function` is expected to be an Embeddings object, support for passing in a function will soon be removed.
2025-07-17 21:58:33,409 - INFO - Successfully loaded vector store
2025-07-17 21:58:33,409 - INFO - Successfully loaded vector store
2025-07-17 21:58:33,409 - INFO - Loading LLM...
2025-07-17 21:58:33,409 - INFO - Loading LLM from groq...
2025-07-17 21:58:34,093 - INFO - Successfully loaded LLM from groq
2025-07-17 21:58:34,094 - INFO - Successfully loaded LLM
2025-07-17 21:58:34,094 - INFO - Successfully created the QA chain
2025-07-17 21:58:34,109 - INFO - 127.0.0.1 - - [17/Jul/2025 21:58:34] "POST / HTTP/1.1" 200 -
2025-07-17 22:01:01,254 - INFO - 127.0.0.1 - - [17/Jul/2025 22:01:01] "[32mGET /clear HTTP/1.1[0m" 302 -
2025-07-17 22:01:01,263 - INFO - 127.0.0.1 - - [17/Jul/2025 22:01:01] "GET / HTTP/1.1" 200 -
2025-07-17 22:01:19,463 - INFO - Loading vector store...
2025-07-17 22:01:19,463 - INFO - Loading vector store...
2025-07-17 22:01:19,555 - WARNING - `embedding_function` is expected to be an Embeddings object, support for passing in a function will soon be removed.
2025-07-17 22:01:19,555 - INFO - Successfully loaded vector store
2025-07-17 22:01:19,555 - INFO - Successfully loaded vector store
2025-07-17 22:01:19,555 - INFO - Loading LLM...
2025-07-17 22:01:19,555 - INFO - Loading LLM from groq...
2025-07-17 22:01:19,897 - INFO - Successfully loaded LLM from groq
2025-07-17 22:01:19,897 - INFO - Successfully loaded LLM
2025-07-17 22:01:19,898 - INFO - Successfully created the QA chain
2025-07-17 22:01:19,903 - INFO - 127.0.0.1 - - [17/Jul/2025 22:01:19] "POST / HTTP/1.1" 200 -
2025-07-17 22:03:59,098 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-17 22:03:59,098 - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 22:04:09,033 - INFO - 127.0.0.1 - - [17/Jul/2025 22:04:09] "GET / HTTP/1.1" 200 -
2025-07-17 22:05:04,365 - INFO - Loading vector store...
2025-07-17 22:05:04,365 - INFO - Loading OpenAI embedding model...
2025-07-17 22:05:05,480 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:05:05,592 - INFO - Loading faiss with AVX512 support.
2025-07-17 22:05:05,593 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-07-17 22:05:05,593 - INFO - Loading faiss with AVX2 support.
2025-07-17 22:05:05,677 - INFO - Successfully loaded faiss with AVX2 support.
2025-07-17 22:05:05,686 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-17 22:05:05,834 - INFO - Successfully loaded vector store
2025-07-17 22:05:05,834 - INFO - Loading LLM from groq...
2025-07-17 22:05:06,514 - INFO - Successfully loaded LLM from groq
2025-07-17 22:05:06,515 - INFO - Successfully created the QA chain
2025-07-17 22:05:08,080 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-17 22:05:09,009 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-17 22:05:09,037 - INFO - 127.0.0.1 - - [17/Jul/2025 22:05:09] "[32mPOST / HTTP/1.1[0m" 302 -
2025-07-17 22:05:09,047 - INFO - 127.0.0.1 - - [17/Jul/2025 22:05:09] "GET / HTTP/1.1" 200 -
2025-07-17 22:07:00,431 - INFO - 127.0.0.1 - - [17/Jul/2025 22:07:00] "GET / HTTP/1.1" 200 -
2025-07-17 22:07:06,671 - INFO - 127.0.0.1 - - [17/Jul/2025 22:07:06] "[32mGET /clear HTTP/1.1[0m" 302 -
2025-07-17 22:07:06,679 - INFO - 127.0.0.1 - - [17/Jul/2025 22:07:06] "GET / HTTP/1.1" 200 -
2025-07-17 22:21:38,756 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-17 22:21:38,757 - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 22:21:46,739 - INFO - 127.0.0.1 - - [17/Jul/2025 22:21:46] "GET / HTTP/1.1" 200 -
2025-07-17 22:21:49,434 - INFO - 127.0.0.1 - - [17/Jul/2025 22:21:49] "GET / HTTP/1.1" 200 -
2025-07-17 22:22:15,176 - INFO - Loading vector store...
2025-07-17 22:22:15,177 - INFO - Loading OpenAI embedding model...
2025-07-17 22:22:15,983 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:22:16,083 - INFO - Loading faiss with AVX512 support.
2025-07-17 22:22:16,084 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-07-17 22:22:16,084 - INFO - Loading faiss with AVX2 support.
2025-07-17 22:22:16,160 - INFO - Successfully loaded faiss with AVX2 support.
2025-07-17 22:22:16,175 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-17 22:22:16,345 - INFO - Successfully loaded vector store
2025-07-17 22:22:16,345 - INFO - Loading LLM from groq...
2025-07-17 22:22:17,127 - INFO - Successfully loaded LLM from groq
2025-07-17 22:22:17,128 - INFO - Successfully created the QA chain
2025-07-17 22:22:17,323 - INFO - 127.0.0.1 - - [17/Jul/2025 22:22:17] "POST / HTTP/1.1" 200 -
2025-07-17 22:26:49,116 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-17 22:26:49,116 - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 22:26:54,733 - INFO - 127.0.0.1 - - [17/Jul/2025 22:26:54] "GET / HTTP/1.1" 200 -
2025-07-17 22:27:09,197 - INFO - Loading vector store...
2025-07-17 22:27:09,198 - INFO - Loading OpenAI embedding model...
2025-07-17 22:27:10,186 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:27:10,274 - INFO - Loading faiss with AVX512 support.
2025-07-17 22:27:10,275 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-07-17 22:27:10,275 - INFO - Loading faiss with AVX2 support.
2025-07-17 22:27:10,345 - INFO - Successfully loaded faiss with AVX2 support.
2025-07-17 22:27:10,353 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-17 22:27:10,488 - INFO - Successfully loaded vector store
2025-07-17 22:27:10,488 - INFO - Loading LLM from groq...
2025-07-17 22:27:11,145 - INFO - Successfully loaded LLM from groq
2025-07-17 22:27:11,146 - INFO - Successfully created the QA chain
2025-07-17 22:27:11,315 - INFO - 127.0.0.1 - - [17/Jul/2025 22:27:11] "POST / HTTP/1.1" 200 -
2025-07-17 22:31:02,476 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-17 22:31:02,477 - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 22:31:11,862 - INFO - 127.0.0.1 - - [17/Jul/2025 22:31:11] "GET / HTTP/1.1" 200 -
2025-07-17 22:31:21,517 - INFO - Loading vector store...
2025-07-17 22:31:21,517 - INFO - Loading OpenAI embedding model...
2025-07-17 22:31:22,499 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:31:22,596 - INFO - Loading faiss with AVX512 support.
2025-07-17 22:31:22,597 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-07-17 22:31:22,598 - INFO - Loading faiss with AVX2 support.
2025-07-17 22:31:22,679 - INFO - Successfully loaded faiss with AVX2 support.
2025-07-17 22:31:22,688 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-17 22:31:22,845 - INFO - Successfully loaded vector store
2025-07-17 22:31:22,846 - INFO - Loading LLM from groq...
2025-07-17 22:31:23,574 - INFO - Successfully loaded LLM from groq
2025-07-17 22:31:23,575 - INFO - Successfully created the QA chain
2025-07-17 22:31:23,864 - INFO - 127.0.0.1 - - [17/Jul/2025 22:31:23] "POST / HTTP/1.1" 200 -
2025-07-17 22:41:32,400 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-17 22:41:32,400 - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 22:41:43,154 - INFO - 127.0.0.1 - - [17/Jul/2025 22:41:43] "GET / HTTP/1.1" 200 -
2025-07-17 22:42:00,613 - INFO - Loading vector store...
2025-07-17 22:42:00,614 - INFO - Loading OpenAI embedding model...
2025-07-17 22:42:01,313 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:42:01,424 - INFO - Successfully loaded vector store
2025-07-17 22:42:01,424 - INFO - Loading LLM from groq...
2025-07-17 22:42:02,057 - INFO - Successfully loaded LLM from groq
2025-07-17 22:42:02,057 - INFO - Successfully created the QA chain
2025-07-17 22:42:02,060 - INFO - 127.0.0.1 - - [17/Jul/2025 22:42:02] "POST / HTTP/1.1" 200 -
2025-07-17 22:42:20,973 - INFO - 127.0.0.1 - - [17/Jul/2025 22:42:20] "GET / HTTP/1.1" 200 -
2025-07-17 22:42:21,234 - INFO - 127.0.0.1 - - [17/Jul/2025 22:42:21] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-17 22:42:28,883 - INFO - Loading vector store...
2025-07-17 22:42:28,883 - INFO - Loading OpenAI embedding model...
2025-07-17 22:42:29,520 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:42:29,594 - INFO - Successfully loaded vector store
2025-07-17 22:42:29,594 - INFO - Loading LLM from groq...
2025-07-17 22:42:30,189 - INFO - Successfully loaded LLM from groq
2025-07-17 22:42:30,190 - INFO - Successfully created the QA chain
2025-07-17 22:42:30,194 - INFO - 127.0.0.1 - - [17/Jul/2025 22:42:30] "POST / HTTP/1.1" 200 -
2025-07-17 22:43:41,983 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-17 22:43:41,983 - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 22:44:03,200 - INFO - Loading vector store...
2025-07-17 22:44:03,201 - INFO - Loading OpenAI embedding model...
2025-07-17 22:44:03,547 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:44:03,592 - INFO - Successfully loaded vector store
2025-07-17 22:44:03,593 - INFO - Loading LLM from groq...
2025-07-17 22:44:03,940 - INFO - Successfully loaded LLM from groq
2025-07-17 22:44:03,940 - INFO - Successfully created the QA chain
2025-07-17 22:44:03,943 - INFO - 127.0.0.1 - - [17/Jul/2025 22:44:03] "POST / HTTP/1.1" 200 -
2025-07-17 22:44:27,876 - INFO - 127.0.0.1 - - [17/Jul/2025 22:44:27] "GET / HTTP/1.1" 200 -
2025-07-17 22:44:34,551 - INFO - 127.0.0.1 - - [17/Jul/2025 22:44:34] "GET / HTTP/1.1" 200 -
2025-07-17 22:44:48,673 - INFO - Loading vector store...
2025-07-17 22:44:48,673 - INFO - Loading OpenAI embedding model...
2025-07-17 22:44:49,578 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:44:49,681 - INFO - Successfully loaded vector store
2025-07-17 22:44:49,682 - INFO - Loading LLM from groq...
2025-07-17 22:44:50,580 - INFO - Successfully loaded LLM from groq
2025-07-17 22:44:50,582 - INFO - Successfully created the QA chain
2025-07-17 22:44:50,586 - INFO - 127.0.0.1 - - [17/Jul/2025 22:44:50] "POST / HTTP/1.1" 200 -
2025-07-17 22:45:34,247 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-17 22:45:34,247 - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 22:45:53,014 - INFO - Loading vector store...
2025-07-17 22:45:53,014 - INFO - Loading OpenAI embedding model...
2025-07-17 22:45:53,362 - INFO - Successfully loaded OpenAI embedding model
2025-07-17 22:45:53,406 - INFO - Successfully loaded vector store
2025-07-17 22:45:53,406 - INFO - Loading LLM from groq...
2025-07-17 22:45:53,781 - INFO - Successfully loaded LLM from groq
2025-07-17 22:45:53,781 - INFO - Successfully created the QA chain
2025-07-17 22:45:53,784 - INFO - 127.0.0.1 - - [17/Jul/2025 22:45:53] "POST / HTTP/1.1" 200 -
