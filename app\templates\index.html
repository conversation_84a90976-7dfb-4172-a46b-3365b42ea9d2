<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>🔍 AI Powered Medical Chatbot</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; }
        .chat-container { max-width: 700px; margin: 0 auto; }
        .message { margin-bottom: 1rem; padding: 0.75rem; border-radius: 10px; }
        .user { background-color: #d1e7ff; text-align: right; }
        .assistant { background-color: #f1f1f1; text-align: left; }
        form { margin-top: 2rem; }
        textarea { width: 100%; height: 3rem; padding: 0.5rem; font-size: 1rem; }
        button { padding: 0.5rem 1rem; font-size: 1rem; margin-top: 0.5rem; }
        .error { color: red; }
        .clear-btn { margin-top: 1rem; }
    </style>
</head>
<body>
    <div class="chat-container">
        <h1>🔍 AI Powered Medical Chatbot</h1>

        {% if error %}
            <p class="error">{{ error }}</p>
        {% endif %}

        <div id="chat-box">
            {% for msg in messages %}
                <div class="message {{ msg.role }}">
                    <strong>{{ msg.role|capitalize }}:</strong><br />
                    {{ msg.content | safe | nl2br }}
                </div>
            {% endfor %}
        </div>

        <form method="post" action="{{ url_for('index') }}">
            <textarea name="prompt" placeholder="Ask a medical question..." required></textarea>
            <br />
            <button type="submit">Send</button>
        </form>

        <form method="get" action="{{ url_for('clear') }}">
            <button type="submit" class="clear-btn">Clear Chat</button>
        </form>
    </div>
</body>
</html>