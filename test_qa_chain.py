#!/usr/bin/env python3
"""
Test script to debug the QA chain issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.components.retriever import create_qa_chain
from app.common.logger import get_logger

logger = get_logger(__name__)

def test_qa_chain():
    try:
        print("Creating QA chain...")
        qa_chain = create_qa_chain()
        
        if qa_chain is None:
            print("ERROR: QA chain could not be created")
            return
        
        print("QA chain created successfully")
        print(f"QA chain type: {type(qa_chain)}")
        
        test_question = "what is diabetes?"
        print(f"Testing with question: {test_question}")
        
        # Try different invocation methods
        try:
            print("Trying invoke method with 'question' parameter...")
            response = qa_chain.invoke({"question": test_question})
            print(f"SUCCESS: Response type: {type(response)}")
            print(f"Response content: {response}")

            # Test response extraction
            if isinstance(response, dict):
                content = response.get("result",
                         response.get("answer",
                         response.get("text",
                         response.get("output", str(response)))))
                print(f"Extracted content: {content}")
            else:
                content = str(response)
                print(f"String content: {content}")

            return response
        except Exception as e:
            print(f"ERROR with invoke(question): {str(e)}")
            import traceback
            traceback.print_exc()
        
        try:
            print("Trying call method with 'question' parameter...")
            response = qa_chain({"question": test_question})
            print(f"SUCCESS: Response type: {type(response)}")
            print(f"Response content: {response}")
            return response
        except Exception as e:
            print(f"ERROR with call(question): {str(e)}")
        
        try:
            print("Trying invoke method with 'query' parameter...")
            response = qa_chain.invoke({"query": test_question})
            print(f"SUCCESS: Response type: {type(response)}")
            print(f"Response content: {response}")
            return response
        except Exception as e:
            print(f"ERROR with invoke(query): {str(e)}")
        
        print("All invocation methods failed")
        
    except Exception as e:
        print(f"ERROR in test_qa_chain: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_qa_chain()
